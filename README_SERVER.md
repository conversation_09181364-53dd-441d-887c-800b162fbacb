# Knowledge Server 使用说明

本项目提供了两种服务器模式来访问知识库：

1. **MCP 服务器模式** - 用于与 MCP (Model Context Protocol) 客户端通信
2. **HTTP 服务器模式** - 提供 REST API 接口

## 安装依赖

首先安装所需的依赖包：

```bash
pip install -r requirements.txt
```

## 运行方式

### 方式一：使用启动脚本（推荐）

#### 启动 MCP 服务器
```bash
python start_server.py mcp
```

#### 启动 HTTP 服务器
```bash
# 默认配置 (127.0.0.1:8000)
python start_server.py http

# 自定义主机和端口
python start_server.py http --host 0.0.0.0 --port 8080

# 开发模式（自动重载）
python start_server.py http --host 0.0.0.0 --port 8080 --reload
```

### 方式二：直接运行服务器

#### MCP 服务器
```bash
python mcp_server.py --stdio
```

#### HTTP 服务器
```bash
python http_server.py --host 0.0.0.0 --port 8080
```

## 服务器参数说明

### MCP 服务器参数
- `--stdio`: 使用标准输入输出模式（必需）
- `--log-level`: 日志级别 (DEBUG, INFO, WARNING, ERROR)

### HTTP 服务器参数
- `--host`: 服务器主机地址（默认: 127.0.0.1）
- `--port`: 服务器端口（默认: 8000）
- `--log-level`: 日志级别 (DEBUG, INFO, WARNING, ERROR)
- `--reload`: 启用自动重载（开发模式）

## HTTP API 接口

### 1. 健康检查
```
GET /health
```

### 2. 根路径信息
```
GET /
```

### 3. 查询知识库
```
POST /query
Content-Type: application/json

{
    "query": "查询文本",
    "limit": 10
}
```

### 4. 获取工具列表
```
GET /tools
```

### 5. API 文档
访问 `http://host:port/docs` 查看完整的 API 文档

## 测试服务器

### 快速健康检查
```bash
python test_server.py --quick
```

### 完整功能测试
```bash
# 默认测试
python test_server.py

# 自定义服务器地址和查询文本
python test_server.py --host 127.0.0.1 --port 8080 --query "人工智能发展"
```

## 日志文件

所有日志文件保存在 `logs/` 目录下：

- `logs/mcp_server.log` - MCP 服务器日志
- `logs/http_server.log` - HTTP 服务器日志
- `logs/start_server.log` - 启动脚本日志
- `logs/test_server.log` - 测试脚本日志

## 使用示例

### 1. 启动 HTTP 服务器并测试

```bash
# 终端1：启动服务器
python start_server.py http --host 0.0.0.0 --port 8080

# 终端2：测试服务器
python test_server.py --host 127.0.0.1 --port 8080
```

### 2. 使用 curl 测试 API

```bash
# 健康检查
curl http://127.0.0.1:8080/health

# 查询知识库
curl -X POST http://127.0.0.1:8080/query \
  -H "Content-Type: application/json" \
  -d '{"query": "人工智能"}'
```

### 3. 使用 Python 客户端

```python
import requests

# 查询知识库
response = requests.post(
    "http://127.0.0.1:8080/query",
    json={"query": "人工智能发展趋势"}
)

if response.status_code == 200:
    data = response.json()
    if data["success"]:
        print("查询结果:", data["data"])
    else:
        print("查询失败:", data["error"])
```

## 故障排除

### 1. 端口被占用
如果遇到端口被占用的错误，请更换端口：
```bash
python start_server.py http --port 8081
```

### 2. 依赖包缺失
确保安装了所有依赖：
```bash
pip install -r requirements.txt
```

### 3. 知识库连接失败
检查 `config.json` 中的数据库配置是否正确。

### 4. 查看详细日志
设置日志级别为 DEBUG：
```bash
python start_server.py http --log-level DEBUG
```

## 开发模式

在开发过程中，建议使用自动重载模式：

```bash
python start_server.py http --reload --log-level DEBUG
```

这样当代码发生变化时，服务器会自动重启。
