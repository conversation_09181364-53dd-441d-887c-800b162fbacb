#!/usr/bin/env python3
"""
HTTP 服务器版本的知识服务
提供 REST API 接口访问知识库
"""

import asyncio
import json
import argparse
from datetime import datetime
from typing import Any, Dict
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn
from knowledge import Knowledge
from logger import Logger

# 设置日志
logger = Logger(log_file="logs/http_server.log", name="HTTP_Server")

# 初始化知识实例
knowledge_instance = Knowledge()

# 创建 FastAPI 应用
app = FastAPI(
    title="Knowledge Server API",
    description="知识库查询服务 HTTP API",
    version="1.0.0"
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 请求模型
class QueryRequest(BaseModel):
    query: str
    limit: int = 10

# 响应模型
class QueryResponse(BaseModel):
    success: bool
    data: str = None
    error: str = None
    timestamp: str

@app.on_event("startup")
async def startup_event():
    """服务启动事件"""
    logger.info("HTTP Knowledge Server 启动完成")
    logger.info("API 文档地址: http://host:port/docs")

@app.on_event("shutdown")
async def shutdown_event():
    """服务关闭事件"""
    logger.info("HTTP Knowledge Server 正在关闭")

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "Knowledge Server API",
        "version": "1.0.0",
        "docs": "/docs",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat()
    }

@app.post("/query", response_model=QueryResponse)
async def query_knowledge(request: QueryRequest):
    """查询知识库"""
    try:
        logger.info(f"收到查询请求: {request.query}")
        
        if not request.query.strip():
            raise HTTPException(status_code=400, detail="查询文本不能为空")
        
        # 调用知识服务
        result = knowledge_instance.get_knowledge_data(request.query, 0)
        
        logger.info(f"查询完成，返回结果长度: {len(result) if result else 0}")
        
        return QueryResponse(
            success=True,
            data=result,
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        logger.error(f"查询知识库时发生错误: {str(e)}", exc_info=True)
        return QueryResponse(
            success=False,
            error=str(e),
            timestamp=datetime.now().isoformat()
        )

@app.get("/tools")
async def list_tools():
    """列出可用工具"""
    return {
        "tools": [
            {
                "name": "get_knowledge_data",
                "description": "获取知识数据，基于查询文本搜索相关文档并返回格式化的知识内容",
                "parameters": {
                    "query": "查询文本，用于搜索相关文档",
                    "limit": "返回结果数量限制（可选）"
                }
            }
        ],
        "timestamp": datetime.now().isoformat()
    }

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="HTTP Knowledge Server")
    parser.add_argument("--host", default="127.0.0.1", help="服务器主机地址 (默认: 127.0.0.1)")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口 (默认: 8000)")
    parser.add_argument("--log-level", choices=["DEBUG", "INFO", "WARNING", "ERROR"], 
                       default="INFO", help="日志级别 (默认: INFO)")
    parser.add_argument("--reload", action="store_true", help="启用自动重载 (开发模式)")
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_arguments()
    
    logger.info("=" * 60)
    logger.info("HTTP Knowledge Server 启动中...")
    logger.info(f"主机地址: {args.host}")
    logger.info(f"端口: {args.port}")
    logger.info(f"日志级别: {args.log_level}")
    logger.info(f"自动重载: {'启用' if args.reload else '禁用'}")
    logger.info(f"API 文档: http://{args.host}:{args.port}/docs")
    logger.info("=" * 60)
    
    try:
        uvicorn.run(
            "http_server:app",
            host=args.host,
            port=args.port,
            log_level=args.log_level.lower(),
            reload=args.reload
        )
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务器...")
    except Exception as e:
        logger.error(f"服务器启动失败: {str(e)}", exc_info=True)
        raise

if __name__ == "__main__":
    main()
