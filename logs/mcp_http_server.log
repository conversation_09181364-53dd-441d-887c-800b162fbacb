2025-08-01 15:58:25,967 - INFO - 收到 MCP 消息: initialize
2025-08-01 15:58:25,968 - INFO - 创建新会话: 63e87550-f516-4368-b37c-b33825c9ab19
2025-08-01 15:58:25,968 - INFO - 处理 JSON-RPC 消息: initialize
2025-08-01 15:58:25,968 - ERROR - 处理 MCP 请求时发生错误: Object of type ServerCapabilities is not JSON serializable
Traceback (most recent call last):
  File "/root/docker_data/rag_project/rag_mcp/http_server.py", line 245, in mcp_endpoint
    content=json.dumps(response_data),
            ~~~~~~~~~~^^^^^^^^^^^^^^^
  File "/root/anaconda3/envs/rag/lib/python3.13/json/__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "/root/anaconda3/envs/rag/lib/python3.13/json/encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "/root/anaconda3/envs/rag/lib/python3.13/json/encoder.py", line 261, in iterencode
    return _iterencode(o, 0)
  File "/root/anaconda3/envs/rag/lib/python3.13/json/encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
                    f'is not JSON serializable')
TypeError: Object of type ServerCapabilities is not JSON serializable
2025-08-01 15:58:26,972 - INFO - 收到 MCP 消息: tools/list
2025-08-01 15:58:26,972 - INFO - 处理 JSON-RPC 消息: tools/list
2025-08-01 15:58:26,972 - INFO - 客户端请求工具列表
2025-08-01 15:58:27,976 - INFO - 收到 MCP 消息: tools/call
2025-08-01 15:58:27,976 - INFO - 处理 JSON-RPC 消息: tools/call
2025-08-01 15:58:27,976 - INFO - 收到工具调用请求: get_knowledge_data, 参数: {'query': '人工智能'}
2025-08-01 15:58:27,976 - INFO - 开始处理知识查询: 人工智能
2025-08-01 15:58:29,570 - INFO - 知识查询完成，返回结果长度: 10681
